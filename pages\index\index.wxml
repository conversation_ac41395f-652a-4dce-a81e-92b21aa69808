<!-- index.wxml -->
<view class="container">
  <view class="page-header">
    <view class="header-content">
      <text class="page-title">我的倒数日</text>
      <text class="header-subtitle">记录生活中的重要时刻</text>
    </view>
    <view class="add-btn" hover-class="btn-hover" bindtap="addCountdown">
      <text class="add-icon">+</text>
    </view>
  </view>

  <view class="countdown-list">
    <block wx:if="{{countdowns.length > 0}}">
      <view class="countdown-item" wx:for="{{countdowns}}" wx:key="_id" bindtap="gotoDetail" data-id="{{item._id}}" hover-class="item-hover">
        <view class="countdown-icon" style="background-color: {{item.color}}">
          <text>{{item.icon || '📅'}}</text>
        </view>
        <view class="countdown-info">
          <text class="countdown-title">{{item.title}}</text>
          <view class="countdown-meta">
            <text class="countdown-date">{{item.date}}</text>
            <text class="countdown-days" style="color: {{item.color}}">{{item.daysLeft}} 天</text>
          </view>
        </view>
      </view>
    </block>
    <view class="empty-state" wx:else>
      <view class="empty-icon">📅</view>
      <text class="empty-text">暂无倒数日</text>
      <text class="empty-subtext">点击右上角"+"按钮添加您的第一个倒数日</text>
    </view>
  </view>
</view>