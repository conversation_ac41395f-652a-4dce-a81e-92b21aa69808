/* statistics.wxss - 统计页面样式 */
.user-profile-card {
  background: var(--card-background);
  border-radius: var(--border-radius-lg);
  margin-bottom: 30rpx;
  box-shadow: var(--shadow);
  overflow: hidden;
  animation: slideDown 0.5s ease;
}

@keyframes slideDown {
  from { opacity: 0; transform: translateY(-20rpx); }
  to { opacity: 1; transform: translateY(0); }
}

.user-profile-content {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(139, 195, 74, 0.1));
  position: relative;
  overflow: hidden;
}

.user-profile-content::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  opacity: 0.5;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: var(--border-radius-circle);
  margin-right: 30rpx;
  border: 4rpx solid #ffffff;
  box-shadow: var(--shadow);
  position: relative;
  z-index: 1;
}

.user-info {
  flex: 1;
  position: relative;
  z-index: 1;
}

.user-nickname {
  font-size: 36rpx;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 8rpx;
  display: block;
}

.user-greeting {
  font-size: 28rpx;
  color: var(--text-color-light);
  opacity: 0.9;
}

.user-stats-summary {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: var(--card-background);
  border-top: 1px solid var(--border-color);
}

.stats-summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.summary-value {
  font-size: 36rpx;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 4rpx;
}

.summary-label {
  font-size: 24rpx;
  color: var(--text-color-light);
}

.stats-divider {
  width: 2rpx;
  height: 60rpx;
  background-color: var(--border-color);
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 0 10rpx;
}

.stats-title {
  font-size: 36rpx;
  font-weight: 700;
  color: var(--text-color);
  position: relative;
}

.stats-title::after {
  content: '';
  position: absolute;
  bottom: -8rpx;
  left: 0;
  width: 60rpx;
  height: 6rpx;
  background: var(--primary-gradient);
  border-radius: 3rpx;
}

.stats-period {
  font-size: 26rpx;
  color: var(--text-color-light);
  background-color: rgba(76, 175, 80, 0.1);
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
}

.stats-card {
  background-color: var(--card-background);
  border-radius: var(--border-radius-lg);
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: var(--shadow);
  animation: fadeIn 0.5s ease;
  position: relative;
  overflow: hidden;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 6rpx;
  height: 100%;
  background: var(--primary-gradient);
  border-radius: 3rpx 0 0 3rpx;
}

.card-header {
  margin-bottom: 30rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 8rpx;
  display: block;
}

.card-subtitle {
  font-size: 24rpx;
  color: var(--text-color-light);
}

.weekly-chart-container {
  position: relative;
  margin-top: 20rpx;
  padding-left: 0; /* 移除左侧空间，因为刻度线已删除 */
  padding-bottom: 0; /* 确保底部没有内边距 */
  display: flex; /* 使用flex布局 */
  flex-direction: column; /* 垂直排列 */
}

/* 刻度线相关样式已移除 */

.weekly-chart {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  height: 100rpx; /* 调整高度，包含柱状图容器高度(40rpx)和底部标签空间 */
  padding: 0; /* 移除内边距，确保精确对齐 */
  position: relative;
  margin-top: 10rpx; /* 添加顶部边距 */
}

/* 简化网格线 */
.weekly-chart::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 40rpx; /* 与柱状图容器高度一致 */
  background-image: linear-gradient(rgba(0, 0, 0, 0.05) 1px, transparent 1px);
  background-size: 100% 100%; /* 只保留一条线 */
  z-index: 0;
  pointer-events: none; /* 确保不会阻止点击事件 */
  border-bottom: 1px solid rgba(0, 0, 0, 0.1); /* 添加底部边框 */
}

.chart-column {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 12%;
}

.column-bar-container {
  height: 40rpx; /* 进一步降低高度 */
  display: flex;
  align-items: flex-end;
  margin-bottom: 15rpx;
  position: relative;
  z-index: 2; /* 确保柱状图在网格线上方 */
  padding: 0; /* 移除可能的内边距 */
  border-bottom: 1px solid transparent; /* 添加透明边框 */
}

.column-bar {
  width: 40rpx;
  background-color: transparent; /* 无值时背景透明 */
  border-radius: 20rpx 20rpx 0 0;
  position: relative;
  overflow: hidden;
  transition: height 0.5s ease; /* 添加高度变化的过渡效果 */
  transform-origin: bottom; /* 确保从底部开始变换 */
}

/* 有值的柱状图样式 */
.column-bar.has-value {
  background-color: rgba(76, 175, 80, 0.1);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1); /* 添加阴影增强立体感 */
  height: 4rpx !important; /* 强制使用固定高度，覆盖其他样式 */
  min-height: 4rpx; /* 确保最小高度与固定高度一致 */
}

.column-bar-fill {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%; /* 固定高度为100% */
  background: var(--primary-gradient);
  border-radius: 20rpx 20rpx 0 0;
  animation: barRise 0.8s ease;
  box-shadow: inset 0 2rpx 8rpx rgba(255, 255, 255, 0.3); /* 内阴影增强立体感 */
  transform-origin: bottom; /* 确保从底部开始变换 */
  will-change: transform; /* 优化动画性能 */
  display: none; /* 默认不显示 */
}

/* 只有在有值的柱状图中才显示填充 */
.column-bar.has-value .column-bar-fill {
  display: block;
}

@keyframes barRise {
  from {
    transform: scaleY(0);
    transform-origin: bottom;
  }
  to {
    transform: scaleY(1);
    transform-origin: bottom;
  }
}

.column-label {
  font-size: 24rpx;
  color: var(--text-color);
  font-weight: 500;
  margin-bottom: 5rpx;
}

.column-value {
  font-size: 26rpx;
  font-weight: 700;
  padding: 2rpx 10rpx;
  border-radius: 10rpx;
}

/* 有值的数值标签样式 */
.column-value.has-value {
  color: var(--primary-color);
  background-color: rgba(76, 175, 80, 0.1);
}

/* 无值的数值标签样式 */
.column-value.no-value {
  color: var(--text-color-light);
  background-color: rgba(0, 0, 0, 0.03);
  font-weight: 500;
}

.type-distribution {
  margin-top: 20rpx;
}

.type-item {
  margin-bottom: 30rpx;
  animation: fadeIn 0.5s ease;
  animation-fill-mode: both;
}

.type-item:nth-child(1) { animation-delay: 0.1s; }
.type-item:nth-child(2) { animation-delay: 0.2s; }
.type-item:nth-child(3) { animation-delay: 0.3s; }
.type-item:nth-child(4) { animation-delay: 0.4s; }
.type-item:nth-child(5) { animation-delay: 0.5s; }

.type-header {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.type-icon {
  width: 24rpx;
  height: 24rpx;
  border-radius: var(--border-radius-circle);
  margin-right: 10rpx;
}

.type-name {
  flex: 1;
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-color);
}

.type-percentage {
  font-size: 26rpx;
  font-weight: 600;
  color: var(--primary-color);
}

.type-bar {
  height: 16rpx;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: 10rpx;
  box-shadow: var(--shadow-inset);
}

.type-progress {
  height: 100%;
  border-radius: 8rpx;
  transition: width 1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.type-details {
  display: flex;
  justify-content: space-between;
  margin-top: 8rpx;
}

.type-duration {
  font-size: 24rpx;
  color: var(--text-color);
}

.type-sessions {
  font-size: 24rpx;
  color: var(--text-color-light);
}