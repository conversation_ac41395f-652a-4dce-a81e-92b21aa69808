/* focus.wxss - 专注模式页面样式 */
.focus-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.timer-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 60rpx;
}

.timer-circle {
  width: 300rpx;
  height: 300rpx;
  border-radius: 50%;
  border: 10rpx solid var(--primary-color);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 40rpx;
  box-shadow: 0 0 20rpx rgba(26, 173, 25, 0.2);
  background-color: #ffffff;
}

.timer-text {
  font-size: 60rpx;
  font-weight: 600;
  color: var(--text-color);
  font-family: monospace;
}

.timer-controls {
  display: flex;
  justify-content: center;
  width: 100%;
}

.timer-controls button {
  margin: 0 20rpx;
  min-width: 200rpx;
}

.start-btn {
  background-color: var(--primary-color);
  color: #ffffff;
}

.pause-btn {
  background-color: var(--warning-color);
  color: #ffffff;
}

.stop-btn {
  background-color: var(--error-color);
  color: #ffffff;
}

.focus-settings {
  background-color: #ffffff;
  border-radius: var(--border-radius);
  padding: 30rpx;
  box-shadow: var(--shadow);
}

.setting-section {
  margin-bottom: 40rpx;
}

.setting-section:last-child {
  margin-bottom: 0;
}

.time-options {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
}

.time-option {
  flex: 1;
  text-align: center;
  padding: 15rpx 0;
  margin: 0 10rpx;
  border-radius: var(--border-radius);
  background-color: #f5f5f5;
  font-size: 28rpx;
  transition: all 0.3s ease;
}

.time-option.active {
  background-color: var(--primary-color);
  color: #ffffff;
}

.picker, .music-selector {
  padding: 20rpx;
  background-color: #f5f5f5;
  border-radius: var(--border-radius);
  margin-top: 20rpx;
}

.music-selector {
  display: flex;
  align-items: center;
}