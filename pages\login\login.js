// pages/login/login.js
const app = getApp();

Page({
  data: {
    canIUseGetUserProfile: false,
    userInfo: null,
    hasUserInfo: false,
    isLoading: false
  },

  onLoad() {
    // 判断是否可以使用 getUserProfile API
    if (wx.getUserProfile) {
      this.setData({
        canIUseGetUserProfile: true
      });
    }

    // 检查是否已经登录
    const userInfo = wx.getStorageSync('userInfo');
    console.log('登录页检查用户信息:', userInfo);

    if (userInfo) {
      app.globalData.userInfo = userInfo;

      // 只有真实登录的用户才显示已登录状态并自动跳转
      if (userInfo.isRealLogin) {
        console.log('检测到真实登录，显示已登录状态并自动跳转');
        this.setData({
          userInfo: userInfo,
          hasUserInfo: true
        });

        // 已登录，延迟一下跳转到首页
        setTimeout(() => {
          this.navigateToIndex();
        }, 1000);
      } else {
        console.log('检测到默认登录，显示登录按钮');
        // 对于默认用户信息，显示登录按钮
        this.setData({
          userInfo: userInfo,
          hasUserInfo: false  // 关键修改：设置为 false，显示登录按钮
        });
      }
    }
  },

  // 使用新接口获取用户信息
  getUserProfile() {
    console.log('尝试获取用户信息');
    this.setData({ isLoading: true });

    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        console.log('获取用户信息成功:', res.userInfo);

        // 保存用户信息到全局数据和本地存储
        // 添加标志表示这是真实登录获取的信息
        const userInfoWithFlag = {
          ...res.userInfo,
          isRealLogin: true
        };
        app.globalData.userInfo = userInfoWithFlag;
        wx.setStorageSync('userInfo', userInfoWithFlag);

        console.log('已保存用户信息到全局数据和本地存储');
        console.log('全局数据:', app.globalData.userInfo);
        console.log('本地存储:', wx.getStorageSync('userInfo'));

        this.setData({
          userInfo: res.userInfo,
          hasUserInfo: true,
          isLoading: false
        });

        // 登录成功，跳转到首页
        this.navigateToIndex();
      },
      fail: (err) => {
        console.error('获取用户信息失败', err);
        this.setData({ isLoading: false });

        // 使用默认用户信息
        const defaultUserInfo = {
          nickName: '微信用户',
          avatarUrl: '/assets/icons/stats.png'
        };

        console.log('使用默认用户信息:', defaultUserInfo);

        // 保存默认用户信息
        const defaultUserInfoWithFlag = {
          ...defaultUserInfo,
          isRealLogin: false
        };
        app.globalData.userInfo = defaultUserInfoWithFlag;
        wx.setStorageSync('userInfo', defaultUserInfoWithFlag);

        // 不设置 hasUserInfo 为 true，这样仍然会显示登录按钮
        this.setData({
          userInfo: defaultUserInfo,
          hasUserInfo: false
        });

        wx.showToast({
          title: '获取用户信息失败，使用默认信息',
          icon: 'none'
        });

        // 直接跳转到首页，不需要延迟
        this.navigateToIndex();
      }
    });
  },

  // 使用旧接口获取用户信息（兼容旧版本）
  getUserInfo(e) {
    console.log('使用旧接口获取用户信息', e);

    if (e.detail.userInfo) {
      console.log('旧接口获取用户信息成功:', e.detail.userInfo);

      // 保存用户信息到全局数据和本地存储
      app.globalData.userInfo = e.detail.userInfo;
      wx.setStorageSync('userInfo', e.detail.userInfo);

      console.log('已保存用户信息到全局数据和本地存储');
      console.log('全局数据:', app.globalData.userInfo);
      console.log('本地存储:', wx.getStorageSync('userInfo'));

      this.setData({
        userInfo: e.detail.userInfo,
        hasUserInfo: true
      });

      // 登录成功，跳转到首页
      this.navigateToIndex();
    } else {
      console.error('旧接口获取用户信息失败');

      // 使用默认用户信息
      const defaultUserInfo = {
        nickName: '微信用户',
        avatarUrl: '/assets/icons/stats.png'
      };

      console.log('使用默认用户信息:', defaultUserInfo);

      // 保存默认用户信息
      const defaultUserInfoWithFlag = {
        ...defaultUserInfo,
        isRealLogin: false
      };
      app.globalData.userInfo = defaultUserInfoWithFlag;
      wx.setStorageSync('userInfo', defaultUserInfoWithFlag);

      // 不设置 hasUserInfo 为 true，这样仍然会显示登录按钮
      this.setData({
        userInfo: defaultUserInfo,
        hasUserInfo: false
      });

      wx.showToast({
        title: '获取用户信息失败，使用默认信息',
        icon: 'none'
      });

      // 直接跳转到首页，不需要延迟
      this.navigateToIndex();
    }
  },

  // 跳转到首页
  navigateToIndex() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  // 跳过登录
  skipLogin() {
    wx.showModal({
      title: '提示',
      content: '登录后才能保存您的数据，确定要跳过登录吗？',
      success: (res) => {
        if (res.confirm) {
          console.log('用户选择跳过登录');

          // 使用默认用户信息
          const defaultUserInfo = {
            nickName: '微信用户',
            avatarUrl: '/assets/icons/stats.png'
          };

          console.log('使用默认用户信息:', defaultUserInfo);

          // 保存默认用户信息
          const defaultUserInfoWithFlag = {
            ...defaultUserInfo,
            isRealLogin: false
          };
          app.globalData.userInfo = defaultUserInfoWithFlag;
          wx.setStorageSync('userInfo', defaultUserInfoWithFlag);

          // 不设置 hasUserInfo 为 true，这样仍然会显示登录按钮
          this.setData({
            userInfo: defaultUserInfo,
            hasUserInfo: false
          });

          this.navigateToIndex();
        }
      }
    });
  }
});
