Page({
  data: {
    countdown: null,
    daysLeft: 0,
    recordedDays: 0,
    countdownId: ''
  },
  onLoad: function (options) {
    const id = options.id;
    this.setData({
      countdownId: id
    });
    this.loadCountdown(id);
  },
  // 添加onShow生命周期函数
  onShow: function () {
    // 如果已有倒数日ID，则重新加载数据
    if (this.data.countdownId) {
      this.loadCountdown(this.data.countdownId);
    }
  },
  loadCountdown: function (id) {
    const countdowns = wx.getStorageSync('countdowns') || [];
    const countdown = countdowns.find(item => item._id === id);

    if (countdown) {
      // 计算剩余天数
      const now = new Date();
      const targetDate = new Date(countdown.date);
      const timeDiff = targetDate - now;
      const daysLeft = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));

      // 计算已记录天数
      let recordedDays = 0;
      if (countdown.createTime) {
        const createDate = new Date(countdown.createTime);
        const recordedTimeDiff = now - createDate;
        recordedDays = Math.floor(recordedTimeDiff / (1000 * 60 * 60 * 24));
      }

      this.setData({
        countdown,
        daysLeft,
        recordedDays
      });
    } else {
      wx.showToast({
        title: '未找到该倒数日',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },
  editCountdown: function () {
    const id = this.data.countdown._id;
    wx.navigateTo({
      url: `/pages/add-countdown/add-countdown?id=${id}&mode=edit`
    });
  },
  deleteCountdown: function () {
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个倒数日吗？',
      success: res => {
        if (res.confirm) {
          const countdowns = wx.getStorageSync('countdowns') || [];
          const newCountdowns = countdowns.filter(item => item._id !== this.data.countdown._id);

          wx.setStorageSync('countdowns', newCountdowns);

          wx.showToast({
            title: '删除成功',
            icon: 'success'
          });

          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        }
      }
    });
  }
});