/* todo-detail.wxss - 待办详情页面样式 */
.todo-card {
  background-color: #ffffff;
  border-radius: var(--border-radius);
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: var(--shadow);
}

.todo-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1px solid var(--border-color);
}

.todo-checkbox {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2px solid var(--border-color);
  margin-right: 20rpx;
  position: relative;
}

.todo-checkbox.checked {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.todo-checkbox.checked::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
  width: 10rpx;
  height: 20rpx;
  border-right: 3rpx solid #ffffff;
  border-bottom: 3rpx solid #ffffff;
}

.todo-title {
  flex: 1;
  font-size: 32rpx;
  font-weight: 500;
}

.todo-title.completed {
  text-decoration: line-through;
  color: var(--text-color-light);
}

.todo-deadline, .todo-description {
  margin-bottom: 20rpx;
  display: flex;
  flex-direction: column;
}

.label {
  font-size: 28rpx;
  color: var(--text-color-light);
  margin-bottom: 10rpx;
}

.edit-form {
  background-color: #ffffff;
  border-radius: var(--border-radius);
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: var(--shadow);
}

.edit-btn {
  background-color: var(--primary-color);
  color: #ffffff;
}

.delete-btn {
  background-color: var(--error-color);
  color: #ffffff;
}

.save-btn {
  background-color: var(--primary-color);
  color: #ffffff;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: var(--text-color);
}