const app = getApp();

Page({
  data: {
    totalFocusTime: 0,        // 总专注时长（分钟）
    weeklyFocusTime: [],      // 本周每天专注时长
    focusTypeDistribution: [], // 专注类型分布
    userInfo: null,           // 用户信息
    hasUserInfo: false,       // 是否有用户信息
    // 颜色配置
    colorPalette: ['#4CAF50', '#2196F3', '#FF9800', '#9C27B0', '#F44336'],
    gradientPalette: ['#8BC34A', '#03A9F4', '#FFC107', '#E91E63', '#FF5252']
  },
  onLoad: function () {
    this.calculateStatistics();
    this.getUserInfo();
  },
  onShow: function () {
    this.calculateStatistics();
    this.getUserInfo();
  },

  // 获取用户信息
  getUserInfo: function () {
    console.log('统计页面尝试获取用户信息');

    // 从全局数据获取用户信息
    const userInfo = app.globalData.userInfo;
    console.log('全局用户信息:', userInfo);

    if (userInfo) {
      console.log('使用全局用户信息');
      this.setData({
        userInfo: userInfo,
        hasUserInfo: true
      });
    } else {
      // 如果全局数据中没有用户信息，尝试从本地存储获取
      console.log('尝试从本地存储获取用户信息');
      const storageUserInfo = wx.getStorageSync('userInfo');
      console.log('本地存储用户信息:', storageUserInfo);

      if (storageUserInfo) {
        console.log('使用本地存储用户信息');
        app.globalData.userInfo = storageUserInfo;
        this.setData({
          userInfo: storageUserInfo,
          hasUserInfo: true
        });
      } else {
        console.log('没有找到用户信息');
        // 如果仍然没有找到用户信息，设置默认值
        this.setData({
          userInfo: {
            nickName: '微信用户',
            avatarUrl: '/assets/icons/stats.png' // 使用默认图标作为头像
          },
          hasUserInfo: true
        });
      }
    }

    // 打印当前页面的数据状态
    console.log('统计页面数据:', this.data);
  },
  // 计算统计数据
  calculateStatistics: function () {
    const records = wx.getStorageSync('focusRecords') || [];

    // 计算总专注时长
    const totalTime = records.reduce((sum, record) => sum + record.duration, 0);

    // 计算本周每天的专注时长
    const weeklyData = this.calculateWeeklyData(records);

    // 计算专注类型分布
    const typeDistribution = this.calculateTypeDistribution(records);

    this.setData({
      totalFocusTime: totalTime,
      weeklyFocusTime: weeklyData,
      focusTypeDistribution: typeDistribution
    });
  },
  // 计算本周每天的专注时长
  calculateWeeklyData: function (records) {
    const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    const today = new Date();
    const weekStart = new Date(today);
    weekStart.setDate(today.getDate() - today.getDay()); // 设置为本周日

    const weeklyData = weekDays.map((day, index) => {
      const date = new Date(weekStart);
      date.setDate(weekStart.getDate() + index);
      const dateStr = `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;

      // 计算当天专注时长
      let dayTotal = 0;
      records.forEach(record => {
        const recordDate = new Date(record.createTime);
        const recordDateStr = `${recordDate.getFullYear()}-${recordDate.getMonth() + 1}-${recordDate.getDate()}`;

        if (recordDateStr === dateStr) {
          dayTotal += record.duration;
        }
      });

      return {
        day,
        date: dateStr,
        duration: dayTotal
      };
    });

    return weeklyData;
  },
  // 计算专注类型分布
  calculateTypeDistribution: function (records) {
    const typeMap = {};
    const typeCountMap = {}; // 记录每种类型的专注次数

    records.forEach(record => {
      if (!typeMap[record.type]) {
        typeMap[record.type] = 0;
        typeCountMap[record.type] = 0;
      }
      typeMap[record.type] += record.duration;
      typeCountMap[record.type] += 1;
    });

    // 添加除零保护
    const totalTime = this.data.totalFocusTime || 1; // 如果为0则使用1作为默认值

    const distribution = Object.keys(typeMap).map((type, index) => ({
      type,
      duration: typeMap[type],
      count: typeCountMap[type], // 添加专注次数
      percentage: totalTime > 0 ? Math.round((typeMap[type] / totalTime) * 100) : 0,
      // 预计算颜色
      primaryColor: this.data.colorPalette[index % 5],
      gradientColor: this.data.gradientPalette[index % 5],
      iconColor: this.data.colorPalette[index % 5],
      // 添加颜色索引，用于CSS类名
      colorIndex: index % 5,
      // 预计算完整的样式字符串
      progressStyle: `width: ${totalTime > 0 ? Math.round((typeMap[type] / totalTime) * 100) : 0}%; background: linear-gradient(to right, ${this.data.colorPalette[index % 5]}, ${this.data.gradientPalette[index % 5]})`
    }));

    return distribution;
  }
});