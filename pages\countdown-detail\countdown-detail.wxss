/* countdown-detail.wxss - 倒数日详情页面样式 */
.countdown-card {
  border-radius: 24rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.15);
  color: #ffffff;
  overflow: hidden;
  position: relative;
}

.card-content {
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  min-height: 300rpx;
}

.countdown-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 40rpx;
}

.countdown-title-container {
  display: flex;
  flex-direction: column;
}

.countdown-title {
  font-size: 42rpx;
  font-weight: 700;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.countdown-date {
  font-size: 28rpx;
  opacity: 0.9;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  align-self: flex-start;
}

.countdown-days-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 20rpx 0;
}

.days-circle {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1);
  border: 4rpx solid rgba(255, 255, 255, 0.4);
}

.days-number {
  font-size: 90rpx;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 10rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.days-text {
  font-size: 32rpx;
  opacity: 0.9;
}

.detail-section {
  background-color: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.section-header {
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-color);
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 30rpx;
  background-color: var(--primary-color);
  border-radius: 4rpx;
}

.countdown-description {
  padding: 30rpx;
  font-size: 28rpx;
  color: var(--text-color);
  line-height: 1.8;
}

.stats-grid {
  display: flex;
  padding: 30rpx;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
}

.stat-value {
  font-size: 48rpx;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 26rpx;
  color: var(--text-color-light);
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
  padding: 0 20rpx;
}

.action-buttons button {
  flex: 1;
  margin: 0 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50rpx;
  padding: 24rpx 0;
  font-size: 30rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.btn-hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);
}

.btn-icon {
  margin-right: 10rpx;
  font-size: 32rpx;
}

.edit-btn {
  background: linear-gradient(135deg, var(--primary-color), #2ecc71);
  color: #ffffff;
}

.delete-btn {
  background: linear-gradient(135deg, var(--error-color), #ff7875);
  color: #ffffff;
}