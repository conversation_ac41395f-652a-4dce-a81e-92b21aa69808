<!-- focus.wxml -->
<view class="container">
  <view class="focus-header">
    <text class="title">专注模式</text>
    <text class="subtitle">今日已专注 {{totalFocusTime}} 分钟</text>
  </view>
  
  <wxs src="./utils.wxs" module="utils" />

  <view class="timer-container">
    <view class="timer-circle">
      <text class="timer-text">{{utils.formatTime(currentTime)}}</text>
    </view>
    
    <view class="timer-controls">
      <block wx:if="{{!isFocusing}}">
        <button class="start-btn" bindtap="startFocus">开始专注</button>
      </block>
      <block wx:else>
        <button class="{{isPaused ? 'start-btn' : 'pause-btn'}}" bindtap="{{isPaused ? 'startFocus' : 'pauseFocus'}}">
          {{isPaused ? '继续' : '暂停'}}
        </button>
        <button class="stop-btn" bindtap="stopFocus">结束</button>
      </block>
    </view>
  </view>
  
  <view class="focus-settings">
    <view class="setting-section">
      <text class="section-title">专注时长</text>
      <view class="time-options">
        <view class="time-option {{focusTime === 15 ? 'active' : ''}}" bindtap="setFocusTime" data-time="15">15分钟</view>
        <view class="time-option {{focusTime === 25 ? 'active' : ''}}" bindtap="setFocusTime" data-time="25">25分钟</view>
        <view class="time-option {{focusTime === 45 ? 'active' : ''}}" bindtap="setFocusTime" data-time="45">45分钟</view>
        <view class="time-option {{focusTime === 60 ? 'active' : ''}}" bindtap="setFocusTime" data-time="60">60分钟</view>
      </view>
    </view>
    
    <view class="setting-section">
      <text class="section-title">专注类型</text>
      <picker mode="selector" range="{{focusTypes}}" bindchange="setFocusType">
        <view class="picker">
          当前选择：{{focusTypes[focusType]}}
        </view>
      </picker>
    </view>
    
    <view class="setting-section">
      <text class="section-title">背景音乐</text>
      <view class="music-selector" bindtap="selectMusic">
        <text>{{selectedMusic || '选择音乐'}}</text>
      </view>
    </view>
  </view>
</view>