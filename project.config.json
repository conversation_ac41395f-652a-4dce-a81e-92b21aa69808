{"description": "项目配置文件", "miniprogramRoot": "", "compileType": "miniprogram", "setting": {"useCompilerPlugins": ["typescript", "less"], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "coverView": false, "postcss": false, "minified": false, "enhance": false, "showShadowRootInWxmlPanel": false, "packNpmRelationList": [], "ignoreUploadUnusedFiles": true, "compileHotReLoad": false, "skylineRenderEnable": true}, "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {}, "srcMiniprogramRoot": "", "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "libVersion": "3.0.2", "packOptions": {"ignore": [], "include": []}, "appid": "wx97cf7364adcc70c3"}