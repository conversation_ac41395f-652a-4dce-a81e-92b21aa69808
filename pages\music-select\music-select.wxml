<!-- music-select.wxml -->
<view class="container">
  <view class="music-list">
    <view class="music-item {{selectedId === item.id ? 'selected' : ''}}" 
          wx:for="{{musicList}}" 
          wx:key="id"
          bindtap="selectMusic"
          data-id="{{item.id}}">
      <view class="music-icon"></view>
      <text class="music-name">{{item.name}}</text>
      <view class="selected-icon" wx:if="{{selectedId === item.id}}"></view>
    </view>
  </view>
  
  <button class="confirm-btn" bindtap="confirmSelection">确认选择</button>
</view>