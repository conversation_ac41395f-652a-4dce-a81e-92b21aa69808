// calendar.js - 添加周视图和农历显示功能
Page({
  data: {
    year: new Date().getFullYear(),
    month: new Date().getMonth() + 1,
    day: new Date().getDate(),
    days: [],          // 当月天数数组
    todoMap: {},       // 日期-待办事项映射
    focusMap: {},      // 日期-专注记录映射
    selectedDate: '',  // 选中的日期
    todos: [],         // 选中日期的待办事项
    viewMode: 'month', // 视图模式：month-月视图，week-周视图
    weekDays: [],      // 周视图的日期数组
    showLunar: true,   // 是否显示农历
    lunarData: {},     // 农历数据
    weekdayText: '一'  // 选中日期的星期几文本
  },

  // 切换视图模式
  switchViewMode: function () {
    const newMode = this.data.viewMode === 'month' ? 'week' : 'month';
    this.setData({ viewMode: newMode });

    if (newMode === 'week') {
      this.initWeekView();
    } else {
      this.initCalendar();
    }
  },

  // 初始化周视图
  initWeekView: function () {
    const weekDays = [];
    const now = new Date(this.data.year, this.data.month - 1, this.data.day);
    const currentDay = now.getDay(); // 当前是周几

    // 计算本周的起始日期（周日为起始）
    const weekStart = new Date(now);
    weekStart.setDate(now.getDate() - currentDay);

    // 生成本周的日期数组
    for (let i = 0; i < 7; i++) {
      const date = new Date(weekStart);
      date.setDate(weekStart.getDate() + i);

      weekDays.push({
        year: date.getFullYear(),
        month: date.getMonth() + 1,
        day: date.getDate(),
        isToday: date.getFullYear() === new Date().getFullYear() &&
          date.getMonth() === new Date().getMonth() &&
          date.getDate() === new Date().getDate(),
        weekDay: ['日', '一', '二', '三', '四', '五', '六'][i],
        lunar: this.getLunarDate(date) // 获取农历日期
      });
    }

    this.setData({ weekDays });
  },

  // 获取农历日期（简化版，实际实现需要农历算法库）
  getLunarDate: function (date) {
    // 这里需要引入农历转换库或API
    // 简化示例，实际需要完整的农历转换算法
    const lunarData = {
      '2023-5-1': '四月十二',
      '2023-5-2': '四月十三',
      // ... 更多日期映射
    };

    const dateStr = `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
    return lunarData[dateStr] || '';
  },

  // 切换到上一周/下一周
  changeWeek: function (e) {
    const direction = e.currentTarget.dataset.direction;
    const firstDay = new Date(this.data.weekDays[0].year, this.data.weekDays[0].month - 1, this.data.weekDays[0].day);

    if (direction === 'prev') {
      firstDay.setDate(firstDay.getDate() - 7);
    } else {
      firstDay.setDate(firstDay.getDate() + 7);
    }

    this.setData({
      year: firstDay.getFullYear(),
      month: firstDay.getMonth() + 1,
      day: firstDay.getDate()
    });

    this.initWeekView();
  },

  // 切换农历显示
  toggleLunar: function () {
    this.setData({ showLunar: !this.data.showLunar });
  },
  onLoad: function () {
    this.initCalendar();
    this.loadTodoData();
    this.loadFocusData();
  },
  onShow: function () {
    this.loadTodoData();
    this.loadFocusData();
  },
  // 初始化日历
  initCalendar: function () {
    const days = [];
    const date = new Date(this.data.year, this.data.month - 1, 1);
    const firstDay = date.getDay();

    // 获取当月天数
    const lastDate = new Date(this.data.year, this.data.month, 0);
    const lastDay = lastDate.getDate();

    // 填充前面的空白
    for (let i = 0; i < firstDay; i++) {
      days.push({
        day: '',
        isCurrentMonth: false
      });
    }

    // 填充当月日期
    for (let i = 1; i <= lastDay; i++) {
      days.push({
        day: i,
        isCurrentMonth: true,
        isToday: i === this.data.day && new Date().getMonth() + 1 === this.data.month && new Date().getFullYear() === this.data.year
      });
    }

    // 计算当前选中日期是星期几
    const selectedDate = new Date(this.data.year, this.data.month - 1, this.data.day);
    const weekdayIndex = selectedDate.getDay();
    const weekdayNames = ['日', '一', '二', '三', '四', '五', '六'];
    const weekdayText = weekdayNames[weekdayIndex];

    this.setData({
      days: days,
      selectedDate: `${this.data.year}-${this.data.month}-${this.data.day}`,
      weekdayText: weekdayText
    });
  },
  // 加载待办数据
  loadTodoData: function () {
    const todos = wx.getStorageSync('todos') || [];
    const todoMap = {};

    todos.forEach(todo => {
      const date = new Date(todo.deadline);
      const dateStr = `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;

      if (!todoMap[dateStr]) {
        todoMap[dateStr] = [];
      }
      todoMap[dateStr].push(todo);
    });

    this.setData({ todoMap });
    this.updateSelectedDateTodos();
  },
  // 加载专注数据
  loadFocusData: function () {
    const records = wx.getStorageSync('focusRecords') || [];
    const focusMap = {};

    records.forEach(record => {
      const date = new Date(record.createTime);
      const dateStr = `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;

      if (!focusMap[dateStr]) {
        focusMap[dateStr] = [];
      }
      focusMap[dateStr].push(record);
    });

    this.setData({ focusMap });
  },
  // 选择日期
  selectDate: function (e) {
    const day = e.currentTarget.dataset.day;
    if (!day) return;

    const dateStr = `${this.data.year}-${this.data.month}-${day}`;

    // 计算选中日期是星期几
    const selectedDate = new Date(this.data.year, this.data.month - 1, day);
    const weekdayIndex = selectedDate.getDay(); // 0是周日，1是周一，以此类推
    const weekdayNames = ['日', '一', '二', '三', '四', '五', '六'];
    const weekdayText = weekdayNames[weekdayIndex];

    this.setData({
      selectedDate: dateStr,
      weekdayText: weekdayText
    });

    this.updateSelectedDateTodos();
  },
  // 更新选中日期的待办事项
  updateSelectedDateTodos: function () {
    const todos = this.data.todoMap[this.data.selectedDate] || [];
    this.setData({ todos });
  },
  // 切换月份
  changeMonth: function (e) {
    const direction = e.currentTarget.dataset.direction;
    let year = this.data.year;
    let month = this.data.month;
    let day = this.data.day;

    if (direction === 'prev') {
      if (month === 1) {
        year--;
        month = 12;
      } else {
        month--;
      }
    } else {
      if (month === 12) {
        year++;
        month = 1;
      } else {
        month++;
      }
    }

    // 检查新月份的天数是否足够
    const lastDayOfMonth = new Date(year, month, 0).getDate();
    if (day > lastDayOfMonth) {
      day = lastDayOfMonth;
    }

    this.setData({
      year: year,
      month: month,
      day: day
    });

    this.initCalendar();
  },
  // 添加待办事项
  addTodo: function () {
    wx.navigateTo({
      url: `/pages/todo-detail/todo-detail?date=${this.data.selectedDate}`
    });
  },
  // 查看待办详情
  viewTodoDetail: function (e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/todo-detail/todo-detail?id=${id}`
    });
  }
});