<!-- pages/login/login.wxml -->
<view class="container">
  <view class="login-header">
    <view class="logo-container">
      <image class="logo" src="/assets/icons/countdown-active.png" mode="aspectFit"></image>
    </view>
    <text class="app-name">PTU_TODO</text>
    <text class="app-slogan">记录生活中的重要时刻</text>
  </view>

  <view class="login-content">
    <block wx:if="{{!hasUserInfo}}">
      <view class="login-tips">
        <text class="tips-text">登录后可以保存您的数据</text>
      </view>

      <view class="login-buttons">
        <button
          wx:if="{{canIUseGetUserProfile}}"
          class="login-btn"
          bindtap="getUserProfile"
          loading="{{isLoading}}"
        >微信登录</button>

        <button
          wx:else
          class="login-btn"
          open-type="getUserInfo"
          bindgetuserinfo="getUserInfo"
        >微信登录</button>

        <button class="skip-btn" bindtap="skipLogin">暂不登录</button>
      </view>
    </block>

    <block wx:else>
      <view class="user-info">
        <image class="avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
        <text class="nickname">{{userInfo.nickName}}</text>
        <text class="welcome-text">欢迎回来！</text>
      </view>

      <view class="login-success">
        <text>登录成功，正在跳转...</text>
      </view>
    </block>
  </view>

  <view class="login-footer">
    <text class="copyright">© 2025 PTU_TODO</text>
  </view>
</view>
