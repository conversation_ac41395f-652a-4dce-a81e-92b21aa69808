/* app.wxss - 全局样式 */
page {
  /* 主色调 */
  --primary-color: #4CAF50;
  --primary-gradient: linear-gradient(135deg, #4CAF50, #8BC34A);
  --secondary-color: #03A9F4;
  --secondary-gradient: linear-gradient(135deg, #03A9F4, #00BCD4);

  /* 文本颜色 */
  --text-color: #2C3E50;
  --text-color-light: #7F8C8D;
  --text-color-lighter: #BDC3C7;

  /* 背景颜色 */
  --background-color: #E8F5E9; /* 淡绿色背景 */
  --card-background: #FFFFFF;

  /* 边框和装饰 */
  --border-color: #ECEFF1;
  --border-radius: 16rpx;
  --border-radius-lg: 24rpx;
  --border-radius-circle: 50%;

  /* 功能色 */
  --success-color: #4CAF50;
  --warning-color: #FF9800;
  --error-color: #F44336;
  --info-color: #2196F3;

  /* 阴影 */
  --shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  --shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  --shadow-inset: inset 0 2rpx 6rpx rgba(0, 0, 0, 0.05);

  /* 动画 */
  --transition-fast: 0.2s ease;
  --transition: 0.3s ease;
  --transition-slow: 0.5s ease;

  /* 字体 */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  font-size: 28rpx;
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--background-color);
}

.container {
  padding: 30rpx;
  box-sizing: border-box;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20rpx); }
  to { opacity: 1; transform: translateY(0); }
}

/* 通用标题样式 */
.title {
  font-size: 38rpx;
  font-weight: 700;
  margin-bottom: 20rpx;
  color: var(--text-color);
  position: relative;
  display: inline-block;
}

.title::after {
  content: '';
  position: absolute;
  bottom: -8rpx;
  left: 0;
  width: 60rpx;
  height: 6rpx;
  background: var(--primary-gradient);
  border-radius: 3rpx;
}

.subtitle {
  font-size: 28rpx;
  color: var(--text-color-light);
  margin-bottom: 16rpx;
  letter-spacing: 0.5rpx;
}

/* 通用按钮样式 */
button {
  border-radius: var(--border-radius);
  font-size: 28rpx;
  font-weight: 600;
  padding: 16rpx 32rpx;
  line-height: 1.5;
  transition: all var(--transition);
  border: none;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

button::after {
  content: '';
  display: block;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  background-image: radial-gradient(circle, rgba(255, 255, 255, 0.3) 10%, transparent 10.01%);
  background-repeat: no-repeat;
  background-position: 50%;
  transform: scale(10, 10);
  opacity: 0;
  transition: transform 0.5s, opacity 0.8s;
}

button:active::after {
  transform: scale(0, 0);
  opacity: 0.3;
  transition: 0s;
}

button.primary {
  background: var(--primary-gradient);
  color: #ffffff;
}

button.secondary {
  background-color: #ffffff;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

button.danger {
  background: linear-gradient(135deg, var(--error-color), #FF5252);
  color: #ffffff;
}

/* 通用表单样式 */
.form-group {
  margin-bottom: 30rpx;
}

.label {
  display: block;
  font-size: 28rpx;
  margin-bottom: 12rpx;
  color: var(--text-color);
  font-weight: 500;
}

.input, .picker, .textarea {
  width: 100%;
  padding: 20rpx;
  border-radius: var(--border-radius);
  background-color: #ffffff;
  border: 1px solid var(--border-color);
  box-sizing: border-box;
  font-size: 28rpx;
  color: var(--text-color);
}

.textarea {
  min-height: 180rpx;
}

.picker {
  display: flex;
  align-items: center;
}

.placeholder {
  color: #999999;
}

/* 卡片样式 */
.card {
  background-color: var(--card-background);
  border-radius: var(--border-radius-lg);
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: var(--shadow);
  transition: transform var(--transition-fast), box-shadow var(--transition-fast);
  overflow: hidden;
  position: relative;
}

.card:active {
  transform: translateY(2rpx);
  box-shadow: var(--shadow-sm);
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 6rpx;
  height: 100%;
  background: var(--primary-gradient);
  border-radius: 3rpx 0 0 3rpx;
}

/* 空状态提示 */
.empty-tip {
  text-align: center;
  padding: 60rpx 0;
  color: var(--text-color-light);
  font-size: 28rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-tip::before {
  content: '';
  display: block;
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23BDC3C7"><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 15.5h-2V13h2v5.5zm0-7.5h-2v-2h2v2z"/></svg>');
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  opacity: 0.5;
}

/* 操作按钮区域 */
.action-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
}

.action-buttons button {
  flex: 1;
  margin: 0 10rpx;
}