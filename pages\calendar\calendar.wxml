<!-- calendar.wxml -->
<view class="container">
  <view class="calendar-header">
    <view class="month-selector">
      <view class="arrow" hover-class="arrow-hover" bindtap="changeMonth" data-direction="prev">
        <view class="arrow-icon prev-icon"></view>
      </view>
      <view class="month-display">
        <text class="year-text">{{year}}年</text>
        <text class="month-text">{{month}}月</text>
      </view>
      <view class="arrow" hover-class="arrow-hover" bindtap="changeMonth" data-direction="next">
        <view class="arrow-icon next-icon"></view>
      </view>
    </view>
  </view>

  <view class="calendar-card">
    <view class="weekdays">
      <view class="weekday weekend">日</view>
      <view class="weekday">一</view>
      <view class="weekday">二</view>
      <view class="weekday">三</view>
      <view class="weekday">四</view>
      <view class="weekday">五</view>
      <view class="weekday weekend">六</view>
    </view>

    <view class="days">
      <view wx:for="{{days}}" wx:key="index"
            class="day {{item.isCurrentMonth ? 'current-month' : ''}} {{item.isToday ? 'today' : ''}} {{selectedDate === year + '-' + month + '-' + item.day ? 'selected' : ''}}"
            hover-class="{{item.day ? 'day-hover' : ''}}"
            bindtap="selectDate" data-day="{{item.day}}">
        <view class="day-content">
          <text class="day-number">{{item.day}}</text>
          <view class="day-indicators">
            <view wx:if="{{item.day && todoMap[year + '-' + month + '-' + item.day]}}" class="todo-dot">
              <text class="todo-count">{{todoMap[year + '-' + month + '-' + item.day].length}}</text>
            </view>
            <view wx:if="{{item.day && focusMap[year + '-' + month + '-' + item.day]}}" class="focus-mark"></view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <view class="selected-date-card">
    <view class="date-header">
      <view class="date-info">
        <text class="selected-date-text">{{selectedDate}}</text>
        <text class="selected-weekday">星期{{weekdayText}}</text>
      </view>
      <button class="add-todo-btn" hover-class="btn-hover" bindtap="addTodo">
        <view class="btn-icon">+</view>
        <text>添加待办</text>
      </button>
    </view>

    <view class="todo-section">
      <view class="section-header">
        <text class="section-title">待办事项</text>
        <text class="todo-count" wx:if="{{todos.length > 0}}">{{todos.length}}项</text>
      </view>

      <view class="todo-list">
        <block wx:if="{{todos.length > 0}}">
          <view class="todo-item" wx:for="{{todos}}" wx:key="_id" bindtap="viewTodoDetail" data-id="{{item._id}}" hover-class="todo-item-hover">
            <view class="todo-checkbox {{item.completed ? 'checked' : ''}}">
              <view wx:if="{{item.completed}}" class="check-icon"></view>
            </view>
            <text class="todo-title {{item.completed ? 'completed' : ''}}">{{item.title}}</text>
          </view>
        </block>
        <view class="empty-tip" wx:else>
          <text>今天暂无待办事项</text>
        </view>
      </view>
    </view>

    <view class="focus-section" wx:if="{{focusMap[selectedDate]}}">
      <view class="section-header">
        <text class="section-title">专注记录</text>
        <text class="focus-count">{{focusMap[selectedDate].length}}次</text>
      </view>

      <view class="focus-list">
        <view class="focus-record" wx:for="{{focusMap[selectedDate]}}" wx:key="_id" hover-class="focus-record-hover">
          <view class="focus-icon"></view>
          <view class="focus-info">
            <text class="focus-type">{{item.type}}</text>
            <text class="focus-time">{{item.startTime}} - {{item.endTime}}</text>
          </view>
          <text class="focus-duration">{{item.duration}}分钟</text>
        </view>
      </view>
    </view>
  </view>
</view>