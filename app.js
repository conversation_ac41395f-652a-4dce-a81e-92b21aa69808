// app.js
App({
  globalData: {
    userInfo: null,
    // 全局数据，可以在页面间共享
    theme: {
      primaryColor: '#1aad19',
      backgroundColor: '#88CCCA'
    }
  },

  onLaunch: function () {
    // 初始化应用时执行，全局只触发一次
    console.log('App launched');

    // 清除用户信息，确保显示登录页面
    this.clearUserInfo();

    // 初始化本地存储，如果不存在则创建
    this.initStorage();

    // 获取用户信息
    this.getUserInfo();
  },

  // 清除用户信息，强制重新登录
  clearUserInfo: function () {
    console.log('清除用户信息，强制重新登录');
    wx.removeStorageSync('userInfo');
    this.globalData.userInfo = null;
  },

  onShow: function (options) {
    // 当小程序启动，或从后台进入前台显示时触发
    console.log('App shown with options:', options);
  },

  onHide: function () {
    // 当小程序从前台进入后台时触发
    console.log('App hidden');
  },

  onError: function (error) {
    // 当小程序发生脚本错误或 API 调用报错时触发
    console.error('App error:', error);
  },

  onPageNotFound: function (options) {
    // 当要打开的页面不存在时触发
    console.error('Page not found:', options.path);

    // 可以在这里进行重定向
    wx.redirectTo({
      url: 'pages/index/index'
    });
  },

  // 初始化本地存储
  initStorage: function () {
    // 初始化倒数日列表
    if (!wx.getStorageSync('countdowns')) {
      wx.setStorageSync('countdowns', []);
    }

    // 初始化待办事项列表
    if (!wx.getStorageSync('todos')) {
      wx.setStorageSync('todos', []);
    }

    // 初始化专注记录
    if (!wx.getStorageSync('focusRecords')) {
      wx.setStorageSync('focusRecords', []);
    }

    // 初始化专注设置
    if (!wx.getStorageSync('focusSettings')) {
      wx.setStorageSync('focusSettings', {
        focusTime: 25,
        breakTime: 5,
        longBreakTime: 15,
        focusGoal: 120
      });
    }
  },

  // 获取用户信息
  getUserInfo: function () {
    console.log('App.js: 尝试获取用户信息');

    // 从本地存储获取用户信息
    const userInfo = wx.getStorageSync('userInfo');
    console.log('App.js: 本地存储用户信息:', userInfo);

    if (userInfo) {
      console.log('App.js: 找到用户信息，设置到全局数据');
      this.globalData.userInfo = userInfo;

      // 如果页面已经注册了回调，则执行回调
      if (this.userInfoReadyCallback) {
        console.log('App.js: 执行用户信息回调');
        this.userInfoReadyCallback({ userInfo });
      }
    } else {
      console.log('App.js: 未找到用户信息');

      // 设置默认用户信息
      const defaultUserInfo = {
        nickName: '微信用户',
        avatarUrl: '/assets/icons/stats.png'
      };

      console.log('App.js: 使用默认用户信息:', defaultUserInfo);
      this.globalData.userInfo = defaultUserInfo;
      wx.setStorageSync('userInfo', defaultUserInfo);
    }

    console.log('App.js: 当前全局用户信息:', this.globalData.userInfo);
  },

  // 检查用户是否已登录
  checkLoginStatus: function () {
    return !!this.globalData.userInfo;
  },

  // 格式化日期为 YYYY-MM-DD
  formatDate: function (date) {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  // 计算两个日期之间的天数差
  getDaysDifference: function (date1, date2) {
    const timeDiff = Math.abs(date2.getTime() - date1.getTime());
    return Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
  },

  // 获取当前日期的字符串表示
  getCurrentDateString: function () {
    return this.formatDate(new Date());
  }
});
