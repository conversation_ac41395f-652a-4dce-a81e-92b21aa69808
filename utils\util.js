// utils/util.js

/**
 * 格式化日期为 YYYY-MM-DD
 * @param {Date} date 日期对象
 * @returns {string} 格式化后的日期字符串
 */
const formatDate = date => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
};

/**
 * 格式化时间为 HH:MM:SS
 * @param {Date} date 日期对象
 * @returns {string} 格式化后的时间字符串
 */
const formatTime = date => {
  const hour = date.getHours().toString().padStart(2, '0');
  const minute = date.getMinutes().toString().padStart(2, '0');
  const second = date.getSeconds().toString().padStart(2, '0');
  return `${hour}:${minute}:${second}`;
};

/**
 * 格式化日期和时间为 YYYY-MM-DD HH:MM:SS
 * @param {Date} date 日期对象
 * @returns {string} 格式化后的日期时间字符串
 */
const formatDateTime = date => {
  return `${formatDate(date)} ${formatTime(date)}`;
};

/**
 * 计算两个日期之间的天数差
 * @param {Date} date1 第一个日期
 * @param {Date} date2 第二个日期
 * @returns {number} 天数差
 */
const getDaysDifference = (date1, date2) => {
  const timeDiff = Math.abs(date2.getTime() - date1.getTime());
  return Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
};

/**
 * 获取当前日期的字符串表示 (YYYY-MM-DD)
 * @returns {string} 当前日期字符串
 */
const getCurrentDateString = () => {
  return formatDate(new Date());
};

/**
 * 获取当前时间的字符串表示 (HH:MM:SS)
 * @returns {string} 当前时间字符串
 */
const getCurrentTimeString = () => {
  return formatTime(new Date());
};

/**
 * 获取当前日期时间的字符串表示 (YYYY-MM-DD HH:MM:SS)
 * @returns {string} 当前日期时间字符串
 */
const getCurrentDateTimeString = () => {
  return formatDateTime(new Date());
};

/**
 * 格式化秒数为 MM:SS 格式
 * @param {number} seconds 秒数
 * @returns {string} 格式化后的时间字符串
 */
const formatSeconds = seconds => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
};

/**
 * 获取一个月的天数
 * @param {number} year 年份
 * @param {number} month 月份 (1-12)
 * @returns {number} 该月的天数
 */
const getDaysInMonth = (year, month) => {
  return new Date(year, month, 0).getDate();
};

/**
 * 获取一个月的第一天是星期几
 * @param {number} year 年份
 * @param {number} month 月份 (1-12)
 * @returns {number} 星期几 (0-6, 0表示星期日)
 */
const getFirstDayOfMonth = (year, month) => {
  return new Date(year, month - 1, 1).getDay();
};

/**
 * 生成日历数据
 * @param {number} year 年份
 * @param {number} month 月份 (1-12)
 * @returns {Array} 日历数据数组
 */
const generateCalendarDays = (year, month) => {
  const daysInMonth = getDaysInMonth(year, month);
  const firstDayOfMonth = getFirstDayOfMonth(year, month);
  
  // 上个月的天数
  const prevMonthDays = month === 1 ? 
    getDaysInMonth(year - 1, 12) : 
    getDaysInMonth(year, month - 1);
  
  const days = [];
  const today = new Date();
  const currentYear = today.getFullYear();
  const currentMonth = today.getMonth() + 1;
  const currentDay = today.getDate();
  
  // 填充上个月的日期
  for (let i = 0; i < firstDayOfMonth; i++) {
    const day = prevMonthDays - firstDayOfMonth + i + 1;
    days.push({
      day,
      isCurrentMonth: false,
      isToday: false
    });
  }
  
  // 填充当前月的日期
  for (let i = 1; i <= daysInMonth; i++) {
    days.push({
      day: i,
      isCurrentMonth: true,
      isToday: year === currentYear && month === currentMonth && i === currentDay
    });
  }
  
  // 填充下个月的日期
  const remainingDays = 42 - days.length; // 6行7列 = 42个日期
  for (let i = 1; i <= remainingDays; i++) {
    days.push({
      day: i,
      isCurrentMonth: false,
      isToday: false
    });
  }
  
  return days;
};

/**
 * 生成唯一ID
 * @returns {string} 唯一ID
 */
const generateUniqueId = () => {
  return Date.now().toString() + Math.floor(Math.random() * 1000).toString();
};

module.exports = {
  formatDate,
  formatTime,
  formatDateTime,
  getDaysDifference,
  getCurrentDateString,
  getCurrentTimeString,
  getCurrentDateTimeString,
  formatSeconds,
  getDaysInMonth,
  getFirstDayOfMonth,
  generateCalendarDays,
  generateUniqueId
};
