<!-- add-countdown.wxml -->
<view class="container">
  <view class="form-header">
    <text class="form-title">{{isEditMode ? '编辑倒数日' : '添加倒数日'}}</text>
    <text class="form-subtitle">记录生活中的重要时刻</text>
  </view>

  <view class="form-card">
    <view class="form-group">
      <view class="input-label">
        <text class="label-icon">📝</text>
        <text class="label">标题</text>
      </view>
      <input class="input" placeholder="请输入倒数日标题" placeholder-class="input-placeholder" value="{{title}}" bindinput="setTitle" />
    </view>

    <view class="form-group">
      <view class="input-label">
        <text class="label-icon">📅</text>
        <text class="label">日期</text>
      </view>
      <picker mode="date" value="{{date}}" bindchange="setDate">
        <view class="picker {{date ? '' : 'placeholder'}}">
          {{date || '请选择日期'}}
          <view class="picker-arrow">▼</view>
        </view>
      </picker>
    </view>

    <view class="form-group">
      <view class="input-label">
        <text class="label-icon">📋</text>
        <text class="label">描述</text>
      </view>
      <textarea class="textarea" placeholder="请输入描述（选填）" placeholder-class="input-placeholder" value="{{description}}" bindinput="setDescription"></textarea>
    </view>

    <view class="form-group">
      <view class="input-label">
        <text class="label-icon">🎨</text>
        <text class="label">颜色</text>
      </view>
      <view class="color-selector">
        <view wx:for="{{colors}}" wx:key="index"
              class="color-item {{color === item ? 'selected' : ''}}"
              style="background-color: {{item}};"
              hover-class="color-hover"
              bindtap="selectColor"
              data-color="{{item}}"></view>
      </view>
    </view>
  </view>

  <view class="preview-section" wx:if="{{title && date}}">
    <text class="preview-title">预览</text>
    <view class="countdown-preview" style="background: linear-gradient(135deg, {{color}}, {{color}}CC)">
      <view class="preview-content">
        <text class="preview-item-title">{{title}}</text>
        <text class="preview-item-date">{{date}}</text>
        <view class="preview-days">
          <text class="preview-days-number">{{getDaysLeft()}}</text>
          <text class="preview-days-text">天</text>
        </view>
      </view>
    </view>
  </view>

  <button class="save-btn" hover-class="btn-hover" bindtap="saveCountdown">
    <text class="btn-icon">💾</text>
    <text>{{isEditMode ? '更新' : '保存'}}</text>
  </button>
</view>