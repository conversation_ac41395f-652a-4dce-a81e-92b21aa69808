// todo-detail.js - 添加分类和优先级功能
Page({
  data: {
    todo: null,
    isEditing: false,
    categories: ['工作', '学习', '生活', '其他'],
    priorities: [
      { value: 'high', label: '高', color: '#ff4d4f' },
      { value: 'medium', label: '中', color: '#faad14' },
      { value: 'low', label: '低', color: '#52c41a' }
    ],
    repeatOptions: [
      { value: 'none', label: '不重复' },
      { value: 'daily', label: '每天' },
      { value: 'weekly', label: '每周' },
      { value: 'monthly', label: '每月' }
    ]
  },
  
  // 设置待办分类
  setCategory: function(e) {
    const category = e.detail.value;
    this.setData({
      'todo.category': this.data.categories[category]
    });
  },
  
  // 设置优先级
  setPriority: function(e) {
    const priority = this.data.priorities[e.detail.value].value;
    this.setData({
      'todo.priority': priority
    });
  },
  
  // 设置重复选项
  setRepeat: function(e) {
    const repeat = this.data.repeatOptions[e.detail.value].value;
    this.setData({
      'todo.repeat': repeat
    });
  },
  
  // 保存待办事项
  saveTodo: function() {
    if (!this.data.todo.title) {
      wx.showToast({
        title: '请输入待办标题',
        icon: 'none'
      });
      return;
    }
    
    const todos = wx.getStorageSync('todos') || [];
    const index = todos.findIndex(item => item._id === this.data.todo._id);
    
    if (index !== -1) {
      todos[index] = this.data.todo;
    } else {
      // 新增待办
      this.data.todo._id = Date.now().toString();
      todos.push(this.data.todo);
    }
    
    wx.setStorageSync('todos', todos);
    
    this.setData({ isEditing: false });
    
    wx.showToast({
      title: '保存成功',
      icon: 'success'
    });
    
    // 如果是重复任务，生成下一个重复任务
    if (this.data.todo.repeat && this.data.todo.repeat !== 'none') {
      this.generateNextRepeatTodo(this.data.todo);
    }
  },
  
  // 生成下一个重复任务
  generateNextRepeatTodo: function(todo) {
    const nextTodo = JSON.parse(JSON.stringify(todo));
    nextTodo._id = Date.now().toString();
    
    const deadline = new Date(todo.deadline);
    
    // 根据重复类型设置下一个截止日期
    switch (todo.repeat) {
      case 'daily':
        deadline.setDate(deadline.getDate() + 1);
        break;
      case 'weekly':
        deadline.setDate(deadline.getDate() + 7);
        break;
      case 'monthly':
        deadline.setMonth(deadline.getMonth() + 1);
        break;
    }
    
    nextTodo.deadline = deadline.toISOString();
    nextTodo.completed = false;
    
    // 将下一个重复任务添加到待办列表
    const todos = wx.getStorageSync('todos') || [];
    todos.push(nextTodo);
    wx.setStorageSync('todos', todos);
  },
  onLoad: function(options) {
    if (options.id) {
      // 编辑现有待办
      this.loadTodo(options.id);
    } else if (options.date) {
      // 新建待办
      const newTodo = {
        title: '',
        description: '',
        deadline: options.date,
        completed: false,
        category: '其他',  // 默认分类
        priority: 'medium',  // 默认优先级
        repeat: 'none'  // 默认不重复
      };
      
      this.setData({
        todo: newTodo,
        isEditing: true  // 直接进入编辑模式
      });
      
      // 设置页面标题
      wx.setNavigationBarTitle({
        title: '添加待办'
      });
    } else {
      // 既没有id也没有date参数
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },
  loadTodo: function(id) {
    const todos = wx.getStorageSync('todos') || [];
    const todo = todos.find(item => item._id === id);
    
    if (todo) {
      this.setData({ todo });
    } else {
      wx.showToast({
        title: '未找到该待办',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },
  toggleComplete: function() {
    const todos = wx.getStorageSync('todos') || [];
    const index = todos.findIndex(item => item._id === this.data.todo._id);
    
    if (index !== -1) {
      todos[index].completed = !todos[index].completed;
      
      wx.setStorageSync('todos', todos);
      
      this.setData({
        'todo.completed': todos[index].completed
      });
      
      wx.showToast({
        title: todos[index].completed ? '已完成' : '已取消完成',
        icon: 'success'
      });
    }
  },
  startEdit: function() {
    this.setData({ isEditing: true });
  },
  cancelEdit: function() {
    this.setData({ isEditing: false });
  },
  updateTitle: function(e) {
    this.setData({
      'todo.title': e.detail.value
    });
  },
  updateDescription: function(e) {
    this.setData({
      'todo.description': e.detail.value
    });
  },
  updateDeadline: function(e) {
    this.setData({
      'todo.deadline': e.detail.value
    });
  },
  deleteTodo: function() {
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个待办事项吗？',
      success: res => {
        if (res.confirm) {
          const todos = wx.getStorageSync('todos') || [];
          const newTodos = todos.filter(item => item._id !== this.data.todo._id);
          
          wx.setStorageSync('todos', newTodos);
          
          wx.showToast({
            title: '删除成功',
            icon: 'success'
          });
          
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        }
      }
    });
  }
});