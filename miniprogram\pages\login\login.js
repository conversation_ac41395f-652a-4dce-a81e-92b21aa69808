// pages/login/login.js
const app = getApp();

Page({
  data: {
    canIUseGetUserProfile: false,
    userInfo: null,
    hasUserInfo: false,
    isLoading: false
  },

  onLoad() {
    // 判断是否可以使用 getUserProfile API
    if (wx.getUserProfile) {
      this.setData({
        canIUseGetUserProfile: true
      });
    }
    
    // 检查是否已经登录
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      app.globalData.userInfo = userInfo;
      this.setData({
        userInfo: userInfo,
        hasUserInfo: true
      });
      
      // 已登录，延迟一下跳转到首页
      setTimeout(() => {
        this.navigateToIndex();
      }, 1000);
    }
  },
  
  // 使用新接口获取用户信息
  getUserProfile() {
    this.setData({ isLoading: true });
    
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        // 保存用户信息到全局数据和本地存储
        app.globalData.userInfo = res.userInfo;
        wx.setStorageSync('userInfo', res.userInfo);
        
        this.setData({
          userInfo: res.userInfo,
          hasUserInfo: true,
          isLoading: false
        });
        
        // 登录成功，跳转到首页
        this.navigateToIndex();
      },
      fail: (err) => {
        console.error('获取用户信息失败', err);
        this.setData({ isLoading: false });
        
        wx.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        });
      }
    });
  },
  
  // 使用旧接口获取用户信息（兼容旧版本）
  getUserInfo(e) {
    if (e.detail.userInfo) {
      // 保存用户信息到全局数据和本地存储
      app.globalData.userInfo = e.detail.userInfo;
      wx.setStorageSync('userInfo', e.detail.userInfo);
      
      this.setData({
        userInfo: e.detail.userInfo,
        hasUserInfo: true
      });
      
      // 登录成功，跳转到首页
      this.navigateToIndex();
    } else {
      wx.showToast({
        title: '获取用户信息失败',
        icon: 'none'
      });
    }
  },
  
  // 跳转到首页
  navigateToIndex() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },
  
  // 跳过登录
  skipLogin() {
    wx.showModal({
      title: '提示',
      content: '登录后才能保存您的数据，确定要跳过登录吗？',
      success: (res) => {
        if (res.confirm) {
          this.navigateToIndex();
        }
      }
    });
  }
});
