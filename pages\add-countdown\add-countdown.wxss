/* add-countdown.wxss - 添加倒数日页面样式 */
.form-header {
  margin-bottom: 40rpx;
  text-align: center;
  padding-bottom: 30rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.2);
}

.form-title {
  font-size: 40rpx;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 10rpx;
  display: block;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.form-subtitle {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}

.form-card {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 40rpx;
}

.form-group {
  margin-bottom: 40rpx;
}

.form-group:last-child {
  margin-bottom: 0;
}

.input-label {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.label-icon {
  font-size: 32rpx;
  margin-right: 10rpx;
}

.label {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-color);
}

.input, .picker, .textarea {
  width: 100%;
  padding: 24rpx;
  border-radius: 12rpx;
  background-color: #f8f8f8;
  border: 1px solid #f0f0f0;
  box-sizing: border-box;
  font-size: 28rpx;
  color: var(--text-color);
  transition: all 0.3s ease;
}

.input:focus, .textarea:focus {
  border-color: var(--primary-color);
  background-color: #ffffff;
}

.input-placeholder {
  color: #bbbbbb;
}

.textarea {
  min-height: 180rpx;
  line-height: 1.6;
}

.picker {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.picker-arrow {
  font-size: 24rpx;
  color: #bbbbbb;
}

.placeholder {
  color: #bbbbbb;
}

.color-selector {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20rpx;
}

.color-item {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  margin-right: 30rpx;
  margin-bottom: 20rpx;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  border: 4rpx solid transparent;
}

.color-hover {
  transform: scale(1.05);
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.15);
}

.color-item.selected {
  border-color: #ffffff;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.2);
}

.color-item.selected::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
  width: 20rpx;
  height: 40rpx;
  border-right: 4rpx solid #ffffff;
  border-bottom: 4rpx solid #ffffff;
}

.preview-section {
  margin-bottom: 40rpx;
}

.preview-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 20rpx;
  display: block;
  position: relative;
  padding-left: 20rpx;
}

.preview-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 30rpx;
  background-color: #ffffff;
  border-radius: 4rpx;
}

.countdown-preview {
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.15);
}

.preview-content {
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #ffffff;
}

.preview-item-title {
  font-size: 36rpx;
  font-weight: 700;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.preview-item-date {
  font-size: 26rpx;
  opacity: 0.9;
  margin-bottom: 30rpx;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
}

.preview-days {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.preview-days-number {
  font-size: 70rpx;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 10rpx;
}

.preview-days-text {
  font-size: 28rpx;
  opacity: 0.9;
}

.save-btn {
  background: linear-gradient(135deg, var(--primary-color), #2ecc71);
  color: #ffffff;
  margin-top: 60rpx;
  border-radius: 50rpx;
  padding: 24rpx 0;
  font-size: 32rpx;
  font-weight: 600;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.btn-hover {
  transform: translateY(-4rpx);
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
}

.btn-icon {
  margin-right: 10rpx;
  font-size: 32rpx;
}