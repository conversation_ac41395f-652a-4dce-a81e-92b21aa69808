/* index.wxss - 倒数日列表页样式 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
  padding: 20rpx 0;
  border-bottom: 1rpx solid rgba(44, 62, 80, 0.2); /* 更改边框颜色，与文字协调 */
}

.header-content {
  display: flex;
  flex-direction: column;
}

.page-title {
  font-size: 44rpx;
  font-weight: 700;
  margin-bottom: 8rpx;
  color: #2C3E50; /* 更深的文字颜色，与背景形成更好的对比 */
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.header-subtitle {
  font-size: 26rpx;
  color: #34495E; /* 更深的副标题颜色 */
  font-weight: 400;
}

.add-btn {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), #2ecc71);
  color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.add-icon {
  font-size: 54rpx;
  font-weight: 300;
  line-height: 1;
}

.btn-hover {
  transform: scale(0.95);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.countdown-list {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 30rpx;
}

.countdown-item {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.item-hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
}

.countdown-item::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 6rpx;
  background-color: var(--primary-color);
}

.countdown-icon {
  width: 110rpx;
  height: 110rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #ffffff;
  font-size: 46rpx;
  margin-right: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.countdown-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.countdown-title {
  font-size: 34rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
  display: block;
  color: var(--text-color);
}

.countdown-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.countdown-date {
  font-size: 26rpx;
  color: var(--text-color-light);
}

.countdown-days {
  font-size: 32rpx;
  font-weight: 700;
  background: rgba(26, 173, 25, 0.1);
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.8; /* 增加不透明度 */
  color: #2C3E50; /* 添加颜色，与文字协调 */
}

.empty-text {
  font-size: 34rpx;
  font-weight: 600;
  color: #2C3E50; /* 更深的文字颜色 */
  margin-bottom: 16rpx;
}

.empty-subtext {
  font-size: 26rpx;
  color: #34495E; /* 更深的副标题颜色 */
  text-align: center;
  max-width: 80%;
  line-height: 1.5;
}

/* 自定义底部导航栏 */
.tab-bar {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 110rpx;
  background-color: #ffffff;
  border-top: 1px solid var(--border-color);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding-bottom: env(safe-area-inset-bottom);
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #999999;
  padding: 10rpx 0;
  transition: all 0.3s ease;
}

.tab-item.active {
  color: var(--primary-color);
  transform: translateY(-4rpx);
}