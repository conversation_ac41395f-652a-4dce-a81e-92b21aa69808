<!-- todo-detail.wxml -->
<view class="container" wx:if="{{todo}}">
  <block wx:if="{{!isEditing}}">
    <view class="todo-card">
      <view class="todo-header">
        <view class="todo-checkbox {{todo.completed ? 'checked' : ''}}" bindtap="toggleComplete"></view>
        <text class="todo-title {{todo.completed ? 'completed' : ''}}">{{todo.title}}</text>
      </view>
      
      <view class="todo-deadline" wx:if="{{todo.deadline}}">
        <text class="label">截止日期：</text>
        <text>{{todo.deadline}}</text>
      </view>
      
      <view class="todo-description" wx:if="{{todo.description}}">
        <text class="label">描述：</text>
        <text>{{todo.description}}</text>
      </view>
    </view>
    
    <view class="action-buttons">
      <button class="edit-btn" bindtap="startEdit">编辑</button>
      <button class="delete-btn" bindtap="deleteTodo">删除</button>
    </view>
  </block>
  
  <block wx:else>
    <view class="edit-form">
      <view class="form-group">
        <text class="label">标题</text>
        <input class="input" value="{{todo.title}}" bindinput="updateTitle" />
      </view>
      
      <view class="form-group">
        <text class="label">截止日期</text>
        <picker mode="date" value="{{todo.deadline}}" bindchange="updateDeadline">
          <view class="picker">
            {{todo.deadline || '请选择日期'}}
          </view>
        </picker>
      </view>
      
      <view class="form-group">
        <text class="label">描述</text>
        <textarea class="textarea" value="{{todo.description}}" bindinput="updateDescription"></textarea>
      </view>
    </view>
    
    <view class="action-buttons">
      <button class="save-btn" bindtap="saveTodo">保存</button>
      <button class="cancel-btn" bindtap="cancelEdit">取消</button>
    </view>
  </block>
</view>