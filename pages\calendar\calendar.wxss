/* calendar.wxss - 日历页面样式 */
.calendar-header {
  margin-bottom: 30rpx;
  animation: slideDown 0.5s ease;
}

@keyframes slideDown {
  from { opacity: 0; transform: translateY(-20rpx); }
  to { opacity: 1; transform: translateY(0); }
}

.month-selector {
  display: flex;
  justify-content: center;
  align-items: center;
  background: var(--card-background);
  border-radius: var(--border-radius-lg);
  padding: 20rpx;
  box-shadow: var(--shadow);
}

.arrow {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--text-color);
  border-radius: var(--border-radius-circle);
  transition: background-color var(--transition-fast);
}

.arrow-hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  border-top: 4rpx solid var(--primary-color);
  border-right: 4rpx solid var(--primary-color);
  transition: transform var(--transition-fast);
}

.prev-icon {
  transform: rotate(-135deg);
}

.next-icon {
  transform: rotate(45deg);
}

.month-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 30rpx;
}

.year-text {
  font-size: 24rpx;
  color: var(--text-color-light);
  margin-bottom: 4rpx;
}

.month-text {
  font-size: 36rpx;
  font-weight: 700;
  color: var(--text-color);
  position: relative;
}

.month-text::after {
  content: '';
  position: absolute;
  bottom: -8rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: var(--primary-gradient);
  border-radius: 2rpx;
}

.calendar-card {
  background-color: var(--card-background);
  border-radius: var(--border-radius-lg);
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: var(--shadow);
  animation: fadeIn 0.5s ease;
}

.weekdays {
  display: flex;
  justify-content: space-around;
  padding: 15rpx 0;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 15rpx;
}

.weekday {
  width: 14.28%;
  text-align: center;
  font-size: 26rpx;
  font-weight: 600;
  color: var(--text-color);
  padding: 10rpx 0;
}

.weekday.weekend {
  color: var(--primary-color);
}

.days {
  display: flex;
  flex-wrap: wrap;
  padding: 10rpx 0;
}

.day {
  width: 14.28%;
  height: 90rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  margin: 5rpx 0;
  transition: all var(--transition-fast);
}

.day-content {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  border-radius: var(--border-radius-circle);
}

.day-hover .day-content {
  background-color: rgba(0, 0, 0, 0.05);
}

.day-number {
  font-size: 28rpx;
  color: var(--text-color-light);
  line-height: 1;
}

.day.current-month .day-number {
  color: var(--text-color);
  font-weight: 500;
}

.day.today .day-content {
  background-color: rgba(76, 175, 80, 0.1);
  border: 2rpx solid var(--primary-color);
}

.day.selected .day-content {
  background: var(--primary-gradient);
  box-shadow: var(--shadow-sm);
}

.day.selected .day-number {
  color: #ffffff;
  font-weight: 600;
}

.day-indicators {
  position: absolute;
  bottom: 5rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 16rpx;
}

.todo-dot {
  width: 32rpx;
  height: 16rpx;
  border-radius: 8rpx;
  background-color: var(--warning-color);
  margin: 0 2rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.todo-count {
  font-size: 12rpx;
  color: #ffffff;
  line-height: 1;
}

.focus-mark {
  width: 16rpx;
  height: 16rpx;
  border-radius: var(--border-radius-circle);
  background-color: var(--primary-color);
  margin: 0 2rpx;
}

.selected-date-card {
  background-color: var(--card-background);
  border-radius: var(--border-radius-lg);
  padding: 30rpx;
  box-shadow: var(--shadow);
  animation: fadeIn 0.5s ease;
}

.date-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1px solid var(--border-color);
}

.date-info {
  display: flex;
  flex-direction: column;
}

.selected-date-text {
  font-size: 32rpx;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 6rpx;
}

.selected-weekday {
  font-size: 24rpx;
  color: var(--text-color-light);
}

.add-todo-btn {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  padding: 12rpx 24rpx;
  background: var(--primary-gradient);
  color: #ffffff;
  border-radius: 30rpx;
  line-height: 1.2;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
}

.btn-hover {
  opacity: 0.9;
  transform: translateY(2rpx);
}

.btn-icon {
  font-size: 32rpx;
  margin-right: 8rpx;
  font-weight: 700;
  line-height: 1;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-color);
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 30rpx;
  background: var(--primary-gradient);
  border-radius: 4rpx;
}

.todo-count, .focus-count {
  font-size: 24rpx;
  color: var(--text-color-light);
  background-color: rgba(76, 175, 80, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.todo-section, .focus-section {
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1px solid var(--border-color);
}

.focus-section {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.todo-list, .focus-list {
  margin-bottom: 10rpx;
}

.todo-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid var(--border-color);
  transition: background-color var(--transition-fast);
}

.todo-item-hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.todo-checkbox {
  width: 40rpx;
  height: 40rpx;
  border-radius: var(--border-radius-circle);
  border: 2px solid var(--border-color);
  margin-right: 20rpx;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all var(--transition-fast);
}

.todo-checkbox.checked {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.check-icon {
  width: 16rpx;
  height: 10rpx;
  border-left: 3rpx solid #ffffff;
  border-bottom: 3rpx solid #ffffff;
  transform: rotate(-45deg);
}

.todo-title {
  flex: 1;
  font-size: 28rpx;
  transition: color var(--transition-fast);
}

.todo-title.completed {
  text-decoration: line-through;
  color: var(--text-color-light);
}

.focus-record {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid var(--border-color);
  transition: background-color var(--transition-fast);
}

.focus-record-hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.focus-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: var(--border-radius-circle);
  background: var(--primary-gradient);
  margin-right: 20rpx;
  position: relative;
}

.focus-icon::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16rpx;
  height: 16rpx;
  border: 3rpx solid #ffffff;
  border-radius: var(--border-radius-circle);
}

.focus-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.focus-type {
  font-size: 28rpx;
  color: var(--text-color);
  margin-bottom: 4rpx;
}

.focus-time {
  font-size: 22rpx;
  color: var(--text-color-light);
}

.focus-duration {
  font-size: 28rpx;
  color: var(--primary-color);
  font-weight: 600;
}