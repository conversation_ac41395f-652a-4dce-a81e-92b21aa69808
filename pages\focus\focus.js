// focus.js
Page({
    data: {
        isFocusing: false,     // 是否正在专注
        isPaused: false,       // 是否暂停
        focusTime: 25,         // 专注时长（分钟）
        breakTime: 5,          // 休息时长（分钟）
        longBreakTime: 15,     // 长休息时长（分钟）
        currentTime: 25 * 60,  // 当前剩余时间（秒）
        timer: null,           // 计时器
        focusType: 'work',     // 专注类型
        focusTypes: ['工作', '学习', '阅读', '思考', '写作'],
        selectedMusic: '',     // 选中的音乐
        startTime: null,       // 开始时间
        totalFocusTime: 0,     // 今日专注总时长（分钟）
        pomodoroCount: 0,      // 番茄钟计数
        isBreakTime: false,    // 是否为休息时间
        focusGoal: 120,        // 每日专注目标（分钟）
        focusMode: 'normal',   // 专注模式：normal-普通，pomodoro-番茄工作法
        interruptions: 0,      // 专注中断次数
        motivationalQuotes: [  // 激励语句
            "专注当下，成就未来",
            "每一分钟的专注都是对自己的投资",
            "坚持专注，突破自我",
            "专注是成功的第一步",
            "今日专注，明日收获"
        ],
        currentQuote: ""       // 当前显示的激励语句
    },

    onLoad: function () {
        // 获取今日专注总时长
        this.getTotalFocusTime();
        // 随机选择一条激励语句
        this.setRandomQuote();
        // 获取用户设置的专注目标
        const focusGoal = wx.getStorageSync('focusGoal');
        if (focusGoal) {
            this.setData({ focusGoal });
        }
    },
    
    // 添加onShow生命周期函数 - 修复：将函数移到Page对象内部
    onShow: function() {
        // 获取选中的音乐名称
        const selectedMusic = wx.getStorageSync('selectedMusic');
        if (selectedMusic) {
            this.setData({ selectedMusic });
        }
        
        // 初始化音频上下文（如果尚未创建）
        if (!this.audioCtx && selectedMusic) {
            this.initAudioContext();
        }
    },

    // 初始化音频上下文 - 修复：将函数移到Page对象内部
    initAudioContext: function() {
        const musicSrc = wx.getStorageSync('selectedMusicSrc');
        if (!musicSrc) return;
        
        this.audioCtx = wx.createInnerAudioContext();
        this.audioCtx.src = musicSrc;
        this.audioCtx.loop = true; // 循环播放
        
        // 监听播放错误
        this.audioCtx.onError((res) => {
            console.error('音频播放错误：', res);
            wx.showToast({
                title: '音频播放出错',
                icon: 'none'
            });
        });
    },

    // 设置随机激励语句
    setRandomQuote: function () {
        const quotes = this.data.motivationalQuotes;
        const randomIndex = Math.floor(Math.random() * quotes.length);
        this.setData({
            currentQuote: quotes[randomIndex]
        });
    },

    // 切换专注模式
    switchFocusMode: function () {
        const newMode = this.data.focusMode === 'normal' ? 'pomodoro' : 'normal';
        this.setData({
            focusMode: newMode,
            pomodoroCount: 0
        });

        wx.showToast({
            title: newMode === 'pomodoro' ? '已切换到番茄工作法' : '已切换到普通模式',
            icon: 'none'
        });
    },

    // 开始专注 - 修改播放音乐部分
    startFocus: function () {
        if (this.data.isFocusing && !this.data.isPaused) return;

        if (!this.data.isFocusing) {
            // 新的专注
            this.setData({
                startTime: new Date(),
                currentTime: this.data.isBreakTime ?
                    (this.data.pomodoroCount % 4 === 0 && this.data.pomodoroCount > 0 ?
                        this.data.longBreakTime * 60 : this.data.breakTime * 60) :
                    this.data.focusTime * 60,
                isFocusing: true,
                isPaused: false,
                interruptions: 0
            });
        } else {
            // 继续暂停的专注
            this.setData({
                isPaused: false
            });
        }

        // 开始计时
        const timer = setInterval(() => {
            if (this.data.currentTime <= 0) {
                // 时间到，保存记录并提示
                this.completeFocus();
                return;
            }

            this.setData({
                currentTime: this.data.currentTime - 1
            });
        }, 1000);

        this.setData({ timer });

        // 播放选中的音乐
        if (this.data.selectedMusic && this.audioCtx) {
            this.audioCtx.play();
        }
    },

    // 完成专注
    completeFocus: function () {
        clearInterval(this.data.timer);

        // 计算本次专注时长
        let focusedTime;
        if (this.data.isBreakTime) {
            // 休息结束，准备下一个专注
            this.setData({
                isBreakTime: false,
                currentTime: this.data.focusTime * 60
            });

            wx.showToast({
                title: '休息结束，准备开始新的专注',
                icon: 'none'
            });
        } else {
            // 专注结束，记录并准备休息
            focusedTime = this.data.focusTime;

            // 保存专注记录
            this.saveFocusRecord(focusedTime);

            if (this.data.focusMode === 'pomodoro') {
                // 番茄工作法模式下，增加番茄钟计数
                const newCount = this.data.pomodoroCount + 1;
                const isLongBreak = newCount % 4 === 0;

                this.setData({
                    pomodoroCount: newCount,
                    isBreakTime: true,
                    currentTime: isLongBreak ? this.data.longBreakTime * 60 : this.data.breakTime * 60
                });

                wx.showToast({
                    title: `专注完成！${isLongBreak ? '开始长休息' : '开始短休息'}`,
                    icon: 'success'
                });
            } else {
                // 普通模式下，直接结束
                this.setData({
                    isFocusing: false,
                    currentTime: this.data.focusTime * 60
                });

                wx.showToast({
                    title: '专注完成！',
                    icon: 'success'
                });
            }
        }

        // 更新随机激励语句
        this.setRandomQuote();
    },

    // 记录专注中断
    recordInterruption: function () {
        if (this.data.isFocusing && !this.data.isPaused && !this.data.isBreakTime) {
            this.setData({
                interruptions: this.data.interruptions + 1
            });

            wx.showToast({
                title: '已记录一次专注中断',
                icon: 'none'
            });
        }
    },

    // 设置专注目标
    setFocusGoal: function (e) {
        const goal = parseInt(e.detail.value);
        if (goal > 0) {
            this.setData({ focusGoal: goal });
            wx.setStorageSync('focusGoal', goal);

            wx.showToast({
                title: '专注目标已设置',
                icon: 'success'
            });
        }
    },

    onUnload: function () {
        // 页面卸载时清除计时器
        if (this.data.timer) {
            clearInterval(this.data.timer);
        }
        
        // 释放音频资源
        if (this.audioCtx) {
            this.audioCtx.stop();
            this.audioCtx.destroy();
        }
    },
    // 选择音乐
    selectMusic: function () {
        wx.navigateTo({
            url: '/pages/music-select/music-select'
        });
    },

    // 设置专注类型
    setFocusType: function (e) {
        const index = e.detail.value;
        this.setData({
            focusType: index
        });
    },

    // 设置专注时长
    setFocusTime: function (e) {
        const time = parseInt(e.currentTarget.dataset.time);
        this.setData({
            focusTime: time,
            currentTime: time * 60
        });
    },

    // 获取今日专注总时长
    getTotalFocusTime: function () {
        const records = wx.getStorageSync('focusRecords') || [];
        const today = new Date();
        const todayStr = `${today.getFullYear()}-${today.getMonth() + 1}-${today.getDate()}`;

        let totalTime = 0;
        records.forEach(record => {
            const recordDate = new Date(record.createTime);
            const recordDateStr = `${recordDate.getFullYear()}-${recordDate.getMonth() + 1}-${recordDate.getDate()}`;

            if (recordDateStr === todayStr) {
                totalTime += record.duration;
            }
        });

        this.setData({ totalFocusTime: totalTime });
    },

    // 保存专注记录
    saveFocusRecord: function (duration) {
        const records = wx.getStorageSync('focusRecords') || [];

        const newRecord = {
            _id: Date.now().toString(),
            type: this.data.focusTypes[this.data.focusType],
            duration: duration,
            createTime: this.data.startTime.getTime()
        };

        records.push(newRecord);
        wx.setStorageSync('focusRecords', records);

        // 更新今日专注总时长
        this.setData({
            totalFocusTime: this.data.totalFocusTime + duration
        });
    },

    // 暂停专注 - 修改暂停音乐部分
    pauseFocus: function () {
        if (this.data.isFocusing && !this.data.isPaused) {
            clearInterval(this.data.timer);
    
            this.setData({
                isPaused: true
            });
    
            // 如果有音乐播放，暂停音乐
            if (this.audioCtx) {
                this.audioCtx.pause();
            }
    
            wx.showToast({
                title: '已暂停',
                icon: 'none'
            });
        }
    },

    // 停止专注 - 修改停止音乐部分
    stopFocus: function () {
        if (this.data.isFocusing) {
            wx.showModal({
                title: '确认结束',
                content: '确定要结束当前专注吗？',
                success: res => {
                    if (res.confirm) {
                        clearInterval(this.data.timer);

                        // 如果不是休息时间且已经专注了一段时间，保存记录
                        if (!this.data.isBreakTime && this.data.startTime) {
                            const now = new Date();
                            const focusedSeconds = Math.floor((now - this.data.startTime) / 1000);
                            const focusedMinutes = Math.floor(focusedSeconds / 60);

                            // 只有专注时间超过1分钟才记录
                            if (focusedMinutes >= 1) {
                                this.saveFocusRecord(focusedMinutes);
                            }
                        }

                        // 重置状态
                        this.setData({
                            isFocusing: false,
                            isPaused: false,
                            currentTime: this.data.focusTime * 60,
                            isBreakTime: false
                        });

                        // 如果有音乐播放，停止音乐
                        if (this.audioCtx) {
                            this.audioCtx.stop();
                        }
    
                        wx.showToast({
                            title: '已结束专注',
                            icon: 'success'
                        });
                    }
                }
            });
        }
    },
    // 格式化时间显示
    formatTime: function (seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes < 10 ? '0' + minutes : minutes}:${remainingSeconds < 10 ? '0' + remainingSeconds : remainingSeconds}`;
    }
});

