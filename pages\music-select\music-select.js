Page({
  data: {
    musicList: [
      { id: 1, name: '轻松爵士', src: '/assets/music/jazz.mp3' },
      { id: 2, name: '自然雨声', src: '/assets/music/rain.mp3' },
      { id: 3, name: '森林鸟鸣', src: '/assets/music/forest.mp3' },
      { id: 4, name: '海浪声', src: '/assets/music/waves.mp3' },
      { id: 5, name: '白噪音', src: '/assets/music/whitenoise.mp3' }
    ],
    selectedId: null,
    audioCtx: null
  },
  onLoad: function(options) {
    // 获取当前选中的音乐ID
    const selectedMusic = wx.getStorageSync('selectedMusic');
    if (selectedMusic) {
      const music = this.data.musicList.find(item => item.name === selectedMusic);
      if (music) {
        this.setData({ selectedId: music.id });
      }
    }
    
    // 创建音频上下文
    this.audioCtx = wx.createInnerAudioContext();
  },
  onUnload: function() {
    // 页面卸载时停止播放
    if (this.audioCtx) {
      this.audioCtx.stop();
    }
  },
  // 选择音乐
  selectMusic: function(e) {
    const id = e.currentTarget.dataset.id;
    const music = this.data.musicList.find(item => item.id === id);
    
    if (music) {
      // 停止当前播放
      if (this.audioCtx) {
        this.audioCtx.stop();
      }
      
      // 设置新的音乐
      this.setData({ selectedId: id });
      
      // 预览播放
      this.audioCtx.src = music.src;
      this.audioCtx.loop = true;
      
      // 添加错误处理
      this.audioCtx.onError((res) => {
        console.error('音频播放错误：', res);
        wx.showToast({
          title: '音频加载失败',
          icon: 'none'
        });
      });
      
      this.audioCtx.play();
    }
  },
  // 确认选择
  confirmSelection: function() {
    if (this.data.selectedId) {
      const music = this.data.musicList.find(item => item.id === this.data.selectedId);
      
      // 保存选择
      wx.setStorageSync('selectedMusic', music.name);
      wx.setStorageSync('selectedMusicSrc', music.src);
      
      // 返回上一页
      wx.navigateBack();
    } else {
      wx.showToast({
        title: '请选择一个音乐',
        icon: 'none'
      });
    }
  }
});