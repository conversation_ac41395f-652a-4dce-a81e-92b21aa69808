/* pages/login/login.wxss */
.container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 100vh;
  padding: 60rpx 40rpx;
  box-sizing: border-box;
  background: linear-gradient(to bottom, var(--background-color), #6AACB5);
}

.login-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 80rpx;
  margin-top: 60rpx;
}

.logo-container {
  width: 180rpx;
  height: 180rpx;
  margin-bottom: 30rpx;
  border-radius: 50%;
  background-color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.15);
}

.logo {
  width: 120rpx;
  height: 120rpx;
}

.app-name {
  font-size: 48rpx;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.app-slogan {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.login-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
}

.login-tips {
  text-align: center;
  margin-bottom: 60rpx;
}

.tips-text {
  font-size: 32rpx;
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.login-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.login-btn {
  width: 80%;
  height: 90rpx;
  border-radius: 45rpx;
  background: linear-gradient(135deg, var(--primary-color), #2ecc71);
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.15);
  border: none;
}

.skip-btn {
  background: transparent;
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
  margin-top: 20rpx;
  border: none;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 60rpx;
}

.avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  border: 4rpx solid #ffffff;
  margin-bottom: 20rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.15);
}

.nickname {
  font-size: 36rpx;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 10rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.welcome-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.login-success {
  text-align: center;
  margin-top: 40rpx;
}

.login-success text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.login-footer {
  text-align: center;
  margin-top: 80rpx;
}

.copyright {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
}
