<!-- countdown-detail.wxml -->
<view class="container" wx:if="{{countdown}}">
  <view class="countdown-card" style="background: linear-gradient(135deg, {{countdown.color}}, {{countdown.color}}CC)">
    <view class="card-content">
      <view class="countdown-header">
        <view class="countdown-title-container">
          <text class="countdown-title">{{countdown.title}}</text>
          <text class="countdown-date">{{countdown.date}}</text>
        </view>
      </view>

      <view class="countdown-days-container">
        <view class="days-circle">
          <text class="days-number">{{daysLeft}}</text>
          <text class="days-text">天</text>
        </view>
      </view>
    </view>
  </view>

  <view class="detail-section" wx:if="{{countdown.description}}">
    <view class="section-header">
      <text class="section-title">描述</text>
    </view>
    <view class="countdown-description">
      <text>{{countdown.description}}</text>
    </view>
  </view>

  <view class="detail-section">
    <view class="section-header">
      <text class="section-title">统计</text>
    </view>
    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-value">{{daysLeft}}</text>
        <text class="stat-label">剩余天数</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{recordedDays}}</text>
        <text class="stat-label">已记录天数</text>
      </view>
    </view>
  </view>

  <view class="action-buttons">
    <button class="edit-btn" hover-class="btn-hover" bindtap="editCountdown">
      <text class="btn-icon">✏️</text>
      <text>编辑</text>
    </button>
    <button class="delete-btn" hover-class="btn-hover" bindtap="deleteCountdown">
      <text class="btn-icon">🗑️</text>
      <text>删除</text>
    </button>
  </view>
</view>