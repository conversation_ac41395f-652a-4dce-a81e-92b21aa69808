<!-- statistics.wxml -->
<view class="container">
  <view class="user-profile-card" wx:if="{{hasUserInfo}}">
    <view class="user-profile-content">
      <image class="user-avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
      <view class="user-info">
        <text class="user-nickname">{{userInfo.nickName}}</text>
        <text class="user-greeting">欢迎回来！</text>
      </view>
    </view>
    <view class="user-stats-summary">
      <view class="stats-summary-item">
        <text class="summary-value">{{totalFocusTime}}</text>
        <text class="summary-label">总专注分钟</text>
      </view>
      <view class="stats-divider"></view>
      <view class="stats-summary-item">
        <text class="summary-value">{{focusTypeDistribution.length || 0}}</text>
        <text class="summary-label">专注类型</text>
      </view>
    </view>
  </view>

  <view class="stats-header">
    <text class="stats-title">专注统计</text>
    <view class="stats-period">本月数据</view>
  </view>

  <view class="stats-card">
    <view class="card-header">
      <text class="card-title">本周专注情况</text>
      <view class="card-subtitle">每日专注时长（分钟）</view>
    </view>
    <view class="weekly-chart-container">
      <!-- 刻度线已移除 -->

      <view class="weekly-chart">
        <view class="chart-column" wx:for="{{weeklyFocusTime}}" wx:key="day">
          <view class="column-bar-container">
            <!-- 修改高度计算方式，进一步降低柱状图高度 -->
            <view class="column-bar {{item.duration > 0 ? 'has-value' : ''}}" style="height: {{item.duration > 0 ? (Math.min(item.duration, 60) / 60 * 0.2) + '%' : '0'}};">
              <view class="column-bar-fill" style="height: 100%;"></view>
            </view>
          </view>
          <text class="column-label">{{item.day}}</text>
          <text class="column-value {{item.duration > 0 ? 'has-value' : 'no-value'}}">{{item.duration}}</text>
        </view>
      </view>
    </view>
  </view>

  <view class="stats-card">
    <view class="card-header">
      <text class="card-title">专注类型分布</text>
      <view class="card-subtitle">按类型统计专注时长</view>
    </view>
    <view class="type-distribution">
      <view class="type-item" wx:for="{{focusTypeDistribution}}" wx:key="type" wx:if="{{focusTypeDistribution.length > 0}}">
        <view class="type-header">
          <view class="type-icon" style="background-color: {{item.iconColor}};"></view>
          <text class="type-name">{{item.type}}</text>
          <text class="type-percentage">{{item.percentage}}%</text>
        </view>
        <view class="type-bar">
          <view class="type-progress" style="{{item.progressStyle}}"></view>
        </view>
        <view class="type-details">
          <text class="type-duration">{{item.duration}}分钟</text>
          <text class="type-sessions">{{item.count || 0}}次专注</text>
        </view>
      </view>
      <view class="empty-tip" wx:if="{{focusTypeDistribution.length === 0}}">
        <text>暂无专注记录</text>
      </view>
    </view>
  </view>
</view>