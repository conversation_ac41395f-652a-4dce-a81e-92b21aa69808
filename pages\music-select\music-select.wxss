/* music-select.wxss - 音乐选择页面样式 */
.music-list {
  background-color: #ffffff;
  border-radius: var(--border-radius);
  padding: 20rpx;
  margin-bottom: 40rpx;
  box-shadow: var(--shadow);
}

.music-item {
  display: flex;
  align-items: center;
  padding: 30rpx 20rpx;
  border-bottom: 1px solid var(--border-color);
  transition: background-color 0.3s ease;
}

.music-item:last-child {
  border-bottom: none;
}

.music-item.selected {
  background-color: rgba(26, 173, 25, 0.1);
}

.music-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: var(--primary-color);
  margin-right: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #ffffff;
  font-size: 30rpx;
}

.music-name {
  flex: 1;
  font-size: 28rpx;
}

.selected-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: var(--primary-color);
  position: relative;
}

.selected-icon::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
  width: 10rpx;
  height: 20rpx;
  border-right: 3rpx solid #ffffff;
  border-bottom: 3rpx solid #ffffff;
}

.confirm-btn {
  background-color: var(--primary-color);
  color: #ffffff;
}