// index.js
const app = getApp();

Page({
  data: {
    countdowns: []
  },
  onLoad: function () {
    this.checkLoginStatus();
    this.getCountdowns();
  },
  onShow: function () {
    this.checkLoginStatus();
    this.getCountdowns();
  },

  // 检查登录状态
  checkLoginStatus: function () {
    // 检查是否已登录
    if (!app.globalData.userInfo && !wx.getStorageSync('userInfo')) {
      wx.redirectTo({
        url: '/pages/login/login'
      });
    }
  },
  getCountdowns: function () {
    // 从本地存储或云数据库获取倒数日列表
    const countdowns = wx.getStorageSync('countdowns') || [];

    // 计算每个倒数日的剩余天数
    const now = new Date();
    countdowns.forEach(item => {
      const targetDate = new Date(item.date);
      const timeDiff = targetDate - now;
      item.daysLeft = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
    });

    this.setData({
      countdowns: countdowns
    });
  },
  addCountdown: function () {
    wx.navigateTo({
      url: '/pages/add-countdown/add-countdown'
    });
  },
  gotoDetail: function (e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/countdown-detail/countdown-detail?id=${id}`
    });
  }
});