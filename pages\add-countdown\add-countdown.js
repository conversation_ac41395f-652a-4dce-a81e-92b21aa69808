Page({
  data: {
    title: '',
    date: '',
    description: '',
    color: '#1aad19',  // 默认颜色
    colors: ['#1aad19', '#2782d7', '#f85f48', '#8d4bbb', '#ff9500']
  },

  // 设置标题
  setTitle: function (e) {
    this.setData({
      title: e.detail.value
    });
  },

  // 设置日期
  setDate: function (e) {
    this.setData({
      date: e.detail.value
    });
  },

  // 设置描述
  setDescription: function (e) {
    this.setData({
      description: e.detail.value
    });
  },

  // 选择颜色
  selectColor: function (e) {
    const color = e.currentTarget.dataset.color;
    this.setData({ color });
  },

  // 保存倒数日
  onLoad: function (options) {
    // 检查是否是编辑模式
    if (options.mode === 'edit' && options.id) {
      // 设置页面标题
      wx.setNavigationBarTitle({
        title: '编辑倒数日'
      });

      const countdowns = wx.getStorageSync('countdowns') || [];
      const countdown = countdowns.find(item => item._id === options.id);

      if (countdown) {
        this.setData({
          isEditMode: true,
          countdownId: options.id,
          title: countdown.title,
          date: countdown.date,
          description: countdown.description,
          color: countdown.color
        });
      }
    } else {
      // 设置页面标题
      wx.setNavigationBarTitle({
        title: '添加倒数日'
      });
    }
  },

  // 计算剩余天数（用于预览）
  getDaysLeft: function () {
    if (!this.data.date) return 0;

    const now = new Date();
    const targetDate = new Date(this.data.date);
    const timeDiff = targetDate - now;
    return Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
  },

  // 修改保存按钮的处理函数
  saveCountdown: function () {
    const { title, date, description, color, isEditMode, countdownId } = this.data;

    if (!title || !date) {
      wx.showToast({
        title: '请填写标题和日期',
        icon: 'none'
      });
      return;
    }

    // 获取现有倒数日
    const countdowns = wx.getStorageSync('countdowns') || [];

    if (isEditMode && countdownId) {
      // 编辑模式：更新现有倒数日
      const index = countdowns.findIndex(item => item._id === countdownId);

      if (index !== -1) {
        // 保留原创建时间
        const createTime = countdowns[index].createTime;

        countdowns[index] = {
          _id: countdownId,
          title,
          date,
          description,
          color,
          createTime  // 保留原创建时间
        };

        wx.setStorageSync('countdowns', countdowns);

        wx.showToast({
          title: '更新成功',
          icon: 'success'
        });
      }
    } else {
      // 添加模式：创建新倒数日
      const newCountdown = {
        _id: Date.now().toString(),
        title,
        date,
        description,
        color,
        createTime: new Date().getTime()
      };

      countdowns.push(newCountdown);
      wx.setStorageSync('countdowns', countdowns);

      wx.showToast({
        title: '添加成功',
        icon: 'success'
      });
    }

    // 返回上一页
    setTimeout(() => {
      wx.navigateBack();
    }, 1500);
  }
});